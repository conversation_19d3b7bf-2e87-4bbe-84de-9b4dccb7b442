"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information";
exports.ids = ["vendor-chunks/property-information"];
exports.modules = {

/***/ "(ssr)/./node_modules/property-information/index.js":
/*!****************************************************!*\
  !*** ./node_modules/property-information/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_7__.find),\n/* harmony export */   hastToReact: () => (/* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__.hastToReact),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   normalize: () => (/* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__.normalize),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(ssr)/./node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/aria.js */ \"(ssr)/./node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/html.js */ \"(ssr)/./node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(ssr)/./node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xlink.js */ \"(ssr)/./node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(ssr)/./node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xml.js */ \"(ssr)/./node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/find.js */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n// Note: types exposed from `index.d.ts`.\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_3__.html, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'html')\n\n\n\n\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDeUM7QUFDUDtBQUNZO0FBQ0g7QUFDUDtBQUNBO0FBQ0o7O0FBRWtCOztBQUUzQyxhQUFhLHlEQUFLLEVBQUUsOENBQUksRUFBRSw4Q0FBUSxFQUFFLGdEQUFLLEVBQUUsZ0RBQUssRUFBRSw0Q0FBRzs7QUFFMUI7QUFDVTs7QUFFckMsWUFBWSx5REFBSyxFQUFFLDhDQUFJLEVBQUUsNENBQU8sRUFBRSxnREFBSyxFQUFFLGdEQUFLLEVBQUUsNENBQUciLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE5vdGU6IHR5cGVzIGV4cG9zZWQgZnJvbSBgaW5kZXguZC50c2AuXG5pbXBvcnQge21lcmdlfSBmcm9tICcuL2xpYi91dGlsL21lcmdlLmpzJ1xuaW1wb3J0IHthcmlhfSBmcm9tICcuL2xpYi9hcmlhLmpzJ1xuaW1wb3J0IHtodG1sIGFzIGh0bWxCYXNlfSBmcm9tICcuL2xpYi9odG1sLmpzJ1xuaW1wb3J0IHtzdmcgYXMgc3ZnQmFzZX0gZnJvbSAnLi9saWIvc3ZnLmpzJ1xuaW1wb3J0IHt4bGlua30gZnJvbSAnLi9saWIveGxpbmsuanMnXG5pbXBvcnQge3htbG5zfSBmcm9tICcuL2xpYi94bWxucy5qcydcbmltcG9ydCB7eG1sfSBmcm9tICcuL2xpYi94bWwuanMnXG5cbmV4cG9ydCB7aGFzdFRvUmVhY3R9IGZyb20gJy4vbGliL2hhc3QtdG8tcmVhY3QuanMnXG5cbmV4cG9ydCBjb25zdCBodG1sID0gbWVyZ2UoW2FyaWEsIGh0bWxCYXNlLCB4bGluaywgeG1sbnMsIHhtbF0sICdodG1sJylcblxuZXhwb3J0IHtmaW5kfSBmcm9tICcuL2xpYi9maW5kLmpzJ1xuZXhwb3J0IHtub3JtYWxpemV9IGZyb20gJy4vbGliL25vcm1hbGl6ZS5qcydcblxuZXhwb3J0IGNvbnN0IHN2ZyA9IG1lcmdlKFthcmlhLCBzdmdCYXNlLCB4bGluaywgeG1sbnMsIHhtbF0sICdzdmcnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/aria.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/aria.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: () => (/* binding */ aria)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/find.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/find.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find)\n/* harmony export */ });\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/**\n * @import {Schema} from 'property-information'\n */\n\n\n\n\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let property = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/hast-to-react.js":
/*!****************************************************************!*\
  !*** ./node_modules/property-information/lib/hast-to-react.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: () => (/* binding */ hastToReact)\n/* harmony export */ });\n/**\n * Special cases for React (`Record<string, string>`).\n *\n * `hast` is close to `React` but differs in a couple of cases.\n * To get a React property from a hast property,\n * check if it is in `hastToReact`.\n * If it is, use the corresponding value;\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL2hhc3QtdG8tcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXGhhc3QtdG8tcmVhY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTcGVjaWFsIGNhc2VzIGZvciBSZWFjdCAoYFJlY29yZDxzdHJpbmcsIHN0cmluZz5gKS5cbiAqXG4gKiBgaGFzdGAgaXMgY2xvc2UgdG8gYFJlYWN0YCBidXQgZGlmZmVycyBpbiBhIGNvdXBsZSBvZiBjYXNlcy5cbiAqIFRvIGdldCBhIFJlYWN0IHByb3BlcnR5IGZyb20gYSBoYXN0IHByb3BlcnR5LFxuICogY2hlY2sgaWYgaXQgaXMgaW4gYGhhc3RUb1JlYWN0YC5cbiAqIElmIGl0IGlzLCB1c2UgdGhlIGNvcnJlc3BvbmRpbmcgdmFsdWU7XG4gKiBvdGhlcndpc2UsIHVzZSB0aGUgaGFzdCBwcm9wZXJ0eS5cbiAqXG4gKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGhhc3RUb1JlYWN0ID0ge1xuICBjbGFzc0lkOiAnY2xhc3NJRCcsXG4gIGRhdGFUeXBlOiAnZGF0YXR5cGUnLFxuICBpdGVtSWQ6ICdpdGVtSUQnLFxuICBzdHJva2VEYXNoQXJyYXk6ICdzdHJva2VEYXNoYXJyYXknLFxuICBzdHJva2VEYXNoT2Zmc2V0OiAnc3Ryb2tlRGFzaG9mZnNldCcsXG4gIHN0cm9rZUxpbmVDYXA6ICdzdHJva2VMaW5lY2FwJyxcbiAgc3Ryb2tlTGluZUpvaW46ICdzdHJva2VMaW5lam9pbicsXG4gIHN0cm9rZU1pdGVyTGltaXQ6ICdzdHJva2VNaXRlcmxpbWl0JyxcbiAgdHlwZU9mOiAndHlwZW9mJyxcbiAgeExpbmtBY3R1YXRlOiAneGxpbmtBY3R1YXRlJyxcbiAgeExpbmtBcmNSb2xlOiAneGxpbmtBcmNyb2xlJyxcbiAgeExpbmtIcmVmOiAneGxpbmtIcmVmJyxcbiAgeExpbmtSb2xlOiAneGxpbmtSb2xlJyxcbiAgeExpbmtTaG93OiAneGxpbmtTaG93JyxcbiAgeExpbmtUaXRsZTogJ3hsaW5rVGl0bGUnLFxuICB4TGlua1R5cGU6ICd4bGlua1R5cGUnLFxuICB4bWxuc1hMaW5rOiAneG1sbnNYbGluaydcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/hast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/html.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/html.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/normalize.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/normalize.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: () => (/* binding */ normalize)\n/* harmony export */ });\n/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL25vcm1hbGl6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEFJXFx3ZWJuZ29ubmd1XFxpbmZpbml0ZS1sYW5ndWFnZVxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFxub3JtYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBHZXQgdGhlIGNsZWFuZWQgY2FzZSBpbnNlbnNpdGl2ZSBmb3JtIG9mIGFuIGF0dHJpYnV0ZSBvciBwcm9wZXJ0eS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqICAgQW4gYXR0cmlidXRlLWxpa2Ugb3IgcHJvcGVydHktbGlrZSBuYW1lLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgVmFsdWUgdGhhdCBjYW4gYmUgdXNlZCB0byBsb29rIHVwIHRoZSBwcm9wZXJseSBjYXNlZCBwcm9wZXJ0eSBvbiBhXG4gKiAgIGBTY2hlbWFgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbm9ybWFsaXplKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/svg.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/svg.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  },\n  space: 'svg',\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseSensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: () => (/* binding */ caseInsensitiveTransform)\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Transformed property.\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0U7O0FBRXBFO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkM7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsU0FBUyxvRkFBc0I7QUFDL0IiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtfSBmcm9tICcuL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1JlY29yZDxzdHJpbmcsIHN0cmluZz59IGF0dHJpYnV0ZXNcbiAqICAgQXR0cmlidXRlcy5cbiAqIEBwYXJhbSB7c3RyaW5nfSBwcm9wZXJ0eVxuICogICBQcm9wZXJ0eS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFRyYW5zZm9ybWVkIHByb3BlcnR5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5KSB7XG4gIHJldHVybiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5LnRvTG93ZXJDYXNlKCkpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: () => (/* binding */ caseSensitiveTransform)\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} attribute\n *   Attribute.\n * @returns {string}\n *   Transformed attribute.\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBhdHRyaWJ1dGVzXG4gKiAgIEF0dHJpYnV0ZXMuXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiAgIEF0dHJpYnV0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFRyYW5zZm9ybWVkIGF0dHJpYnV0ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgYXR0cmlidXRlKSB7XG4gIHJldHVybiBhdHRyaWJ1dGUgaW4gYXR0cmlidXRlcyA/IGF0dHJpYnV0ZXNbYXR0cmlidXRlXSA6IGF0dHJpYnV0ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/create.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/create.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\n\n\n\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nfunction create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(property)] = property\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = property\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(properties, normals, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************!*\
  !*** ./node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: () => (/* binding */ DefinedInfo)\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n/**\n * @import {Space} from 'property-information'\n */\n\n\n\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvZGVmaW5lZC1pbmZvLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25COztBQUU4QjtBQUNLOztBQUVuQywwQkFBMEIsbUNBQW1DO0FBQzdELGNBQWMsc0NBQUs7QUFDbkI7O0FBRU8sMEJBQTBCLDBDQUFJO0FBQ3JDO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxhQUFhLDJCQUEyQjtBQUN4QztBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsc0NBQUssYUFBYSxzQ0FBSztBQUNqRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLGNBQWMsbUJBQW1CO0FBQ2pDO0FBQ0EsV0FBVyxhQUFhO0FBQ3hCO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsV0FBVyxrQkFBa0I7QUFDN0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcZGVmaW5lZC1pbmZvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3BhY2V9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuICovXG5cbmltcG9ydCB7SW5mb30gZnJvbSAnLi9pbmZvLmpzJ1xuaW1wb3J0ICogYXMgdHlwZXMgZnJvbSAnLi90eXBlcy5qcydcblxuY29uc3QgY2hlY2tzID0gLyoqIEB0eXBlIHtSZWFkb25seUFycmF5PGtleW9mIHR5cGVvZiB0eXBlcz59ICovIChcbiAgT2JqZWN0LmtleXModHlwZXMpXG4pXG5cbmV4cG9ydCBjbGFzcyBEZWZpbmVkSW5mbyBleHRlbmRzIEluZm8ge1xuICAvKipcbiAgICogQGNvbnN0cnVjdG9yXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBwcm9wZXJ0eVxuICAgKiAgIFByb3BlcnR5LlxuICAgKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gICAqICAgQXR0cmlidXRlLlxuICAgKiBAcGFyYW0ge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFttYXNrXVxuICAgKiAgIE1hc2suXG4gICAqIEBwYXJhbSB7U3BhY2UgfCB1bmRlZmluZWR9IFtzcGFjZV1cbiAgICogICBTcGFjZS5cbiAgICogQHJldHVybnNcbiAgICogICBJbmZvLlxuICAgKi9cbiAgY29uc3RydWN0b3IocHJvcGVydHksIGF0dHJpYnV0ZSwgbWFzaywgc3BhY2UpIHtcbiAgICBsZXQgaW5kZXggPSAtMVxuXG4gICAgc3VwZXIocHJvcGVydHksIGF0dHJpYnV0ZSlcblxuICAgIG1hcmsodGhpcywgJ3NwYWNlJywgc3BhY2UpXG5cbiAgICBpZiAodHlwZW9mIG1hc2sgPT09ICdudW1iZXInKSB7XG4gICAgICB3aGlsZSAoKytpbmRleCA8IGNoZWNrcy5sZW5ndGgpIHtcbiAgICAgICAgY29uc3QgY2hlY2sgPSBjaGVja3NbaW5kZXhdXG4gICAgICAgIG1hcmsodGhpcywgY2hlY2tzW2luZGV4XSwgKG1hc2sgJiB0eXBlc1tjaGVja10pID09PSB0eXBlc1tjaGVja10pXG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbkRlZmluZWRJbmZvLnByb3RvdHlwZS5kZWZpbmVkID0gdHJ1ZVxuXG4vKipcbiAqIEB0ZW1wbGF0ZSB7a2V5b2YgRGVmaW5lZEluZm99IEtleVxuICogICBLZXkgdHlwZS5cbiAqIEBwYXJhbSB7RGVmaW5lZEluZm99IHZhbHVlc1xuICogICBJbmZvLlxuICogQHBhcmFtIHtLZXl9IGtleVxuICogICBLZXkuXG4gKiBAcGFyYW0ge0RlZmluZWRJbmZvW0tleV19IHZhbHVlXG4gKiAgIFZhbHVlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gbWFyayh2YWx1ZXMsIGtleSwgdmFsdWUpIHtcbiAgaWYgKHZhbHVlKSB7XG4gICAgdmFsdWVzW2tleV0gPSB2YWx1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/info.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/util/info.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: () => (/* binding */ Info)\n/* harmony export */ });\n/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nclass Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGtCQUFrQjtBQUM5Qjs7QUFFQSxXQUFXLFVBQVU7QUFDZDtBQUNQO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8gYXMgSW5mb1R5cGV9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuICovXG5cbi8qKiBAdHlwZSB7SW5mb1R5cGV9ICovXG5leHBvcnQgY2xhc3MgSW5mbyB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHlcbiAgICogICBQcm9wZXJ0eS5cbiAgICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICAgKiAgIEF0dHJpYnV0ZS5cbiAgICogQHJldHVybnNcbiAgICogICBJbmZvLlxuICAgKi9cbiAgY29uc3RydWN0b3IocHJvcGVydHksIGF0dHJpYnV0ZSkge1xuICAgIHRoaXMuYXR0cmlidXRlID0gYXR0cmlidXRlXG4gICAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gIH1cbn1cblxuSW5mby5wcm90b3R5cGUuYXR0cmlidXRlID0gJydcbkluZm8ucHJvdG90eXBlLmJvb2xlYW5pc2ggPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuYm9vbGVhbiA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5jb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuY29tbWFTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuZGVmaW5lZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5tdXN0VXNlUHJvcGVydHkgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUubnVtYmVyID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm92ZXJsb2FkZWRCb29sZWFuID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLnByb3BlcnR5ID0gJydcbkluZm8ucHJvdG90eXBlLnNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLnNwYWNlID0gdW5kZWZpbmVkXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/merge.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: () => (/* binding */ merge)\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nfunction merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6Qjs7QUFFa0M7O0FBRWxDO0FBQ0EsV0FBVyx1QkFBdUI7QUFDbEM7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLHNCQUFzQjtBQUNuQztBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsOENBQU07QUFDbkIiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTcGFjZX0gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG4gKi9cblxuaW1wb3J0IHtTY2hlbWF9IGZyb20gJy4vc2NoZW1hLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxTY2hlbWE+fSBkZWZpbml0aW9uc1xuICogICBEZWZpbml0aW9ucy5cbiAqIEBwYXJhbSB7U3BhY2UgfCB1bmRlZmluZWR9IFtzcGFjZV1cbiAqICAgU3BhY2UuXG4gKiBAcmV0dXJucyB7U2NoZW1hfVxuICogICBTY2hlbWEuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtZXJnZShkZWZpbml0aW9ucywgc3BhY2UpIHtcbiAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBJbmZvPn0gKi9cbiAgY29uc3QgcHJvcGVydHkgPSB7fVxuICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz59ICovXG4gIGNvbnN0IG5vcm1hbCA9IHt9XG5cbiAgZm9yIChjb25zdCBkZWZpbml0aW9uIG9mIGRlZmluaXRpb25zKSB7XG4gICAgT2JqZWN0LmFzc2lnbihwcm9wZXJ0eSwgZGVmaW5pdGlvbi5wcm9wZXJ0eSlcbiAgICBPYmplY3QuYXNzaWduKG5vcm1hbCwgZGVmaW5pdGlvbi5ub3JtYWwpXG4gIH1cblxuICByZXR1cm4gbmV3IFNjaGVtYShwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\n/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nclass Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksNkJBQTZCO0FBQ3pDOztBQUVBLFdBQVcsWUFBWTtBQUNoQjtBQUNQO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQztBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXHNjaGVtYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1NjaGVtYSBhcyBTY2hlbWFUeXBlLCBTcGFjZX0gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG4gKi9cblxuLyoqIEB0eXBlIHtTY2hlbWFUeXBlfSAqL1xuZXhwb3J0IGNsYXNzIFNjaGVtYSB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge1NjaGVtYVR5cGVbJ3Byb3BlcnR5J119IHByb3BlcnR5XG4gICAqICAgUHJvcGVydHkuXG4gICAqIEBwYXJhbSB7U2NoZW1hVHlwZVsnbm9ybWFsJ119IG5vcm1hbFxuICAgKiAgIE5vcm1hbC5cbiAgICogQHBhcmFtIHtTcGFjZSB8IHVuZGVmaW5lZH0gW3NwYWNlXVxuICAgKiAgIFNwYWNlLlxuICAgKiBAcmV0dXJuc1xuICAgKiAgIFNjaGVtYS5cbiAgICovXG4gIGNvbnN0cnVjdG9yKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKSB7XG4gICAgdGhpcy5ub3JtYWwgPSBub3JtYWxcbiAgICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcblxuICAgIGlmIChzcGFjZSkge1xuICAgICAgdGhpcy5zcGFjZSA9IHNwYWNlXG4gICAgfVxuICB9XG59XG5cblNjaGVtYS5wcm90b3R5cGUubm9ybWFsID0ge31cblNjaGVtYS5wcm90b3R5cGUucHJvcGVydHkgPSB7fVxuU2NoZW1hLnByb3RvdHlwZS5zcGFjZSA9IHVuZGVmaW5lZFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/types.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/types.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   booleanish: () => (/* binding */ booleanish),\n/* harmony export */   commaOrSpaceSeparated: () => (/* binding */ commaOrSpaceSeparated),\n/* harmony export */   commaSeparated: () => (/* binding */ commaSeparated),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   overloadedBoolean: () => (/* binding */ overloadedBoolean),\n/* harmony export */   spaceSeparated: () => (/* binding */ spaceSeparated)\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztBQUVPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEFJXFx3ZWJuZ29ubmd1XFxpbmZpbml0ZS1sYW5ndWFnZVxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgcG93ZXJzID0gMFxuXG5leHBvcnQgY29uc3QgYm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgYm9vbGVhbmlzaCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgb3ZlcmxvYWRlZEJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IG51bWJlciA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgc3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGNvbW1hU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBjb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuXG5mdW5jdGlvbiBpbmNyZW1lbnQoKSB7XG4gIHJldHVybiAyICoqICsrcG93ZXJzXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xlink.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xlink.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: () => (/* binding */ xlink)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUVoQyxjQUFjLHVEQUFNO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHhsaW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeGxpbmsgPSBjcmVhdGUoe1xuICBwcm9wZXJ0aWVzOiB7XG4gICAgeExpbmtBY3R1YXRlOiBudWxsLFxuICAgIHhMaW5rQXJjUm9sZTogbnVsbCxcbiAgICB4TGlua0hyZWY6IG51bGwsXG4gICAgeExpbmtSb2xlOiBudWxsLFxuICAgIHhMaW5rU2hvdzogbnVsbCxcbiAgICB4TGlua1RpdGxlOiBudWxsLFxuICAgIHhMaW5rVHlwZTogbnVsbFxuICB9LFxuICBzcGFjZTogJ3hsaW5rJyxcbiAgdHJhbnNmb3JtKF8sIHByb3BlcnR5KSB7XG4gICAgcmV0dXJuICd4bGluazonICsgcHJvcGVydHkuc2xpY2UoNSkudG9Mb3dlckNhc2UoKVxuICB9XG59KVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xml.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/xml.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFaEMsWUFBWSx1REFBTTtBQUN6QixlQUFlLDZDQUE2QztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxceG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeG1sID0gY3JlYXRlKHtcbiAgcHJvcGVydGllczoge3htbEJhc2U6IG51bGwsIHhtbExhbmc6IG51bGwsIHhtbFNwYWNlOiBudWxsfSxcbiAgc3BhY2U6ICd4bWwnLFxuICB0cmFuc2Zvcm0oXywgcHJvcGVydHkpIHtcbiAgICByZXR1cm4gJ3htbDonICsgcHJvcGVydHkuc2xpY2UoMykudG9Mb3dlckNhc2UoKVxuICB9XG59KVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xmlns.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xmlns.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: () => (/* binding */ xmlns)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUNzQzs7QUFFdEUsY0FBYyx1REFBTTtBQUMzQixlQUFlLDBCQUEwQjtBQUN6QyxlQUFlLDhCQUE4QjtBQUM3QztBQUNBLGFBQWEseUZBQXdCO0FBQ3JDLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxceG1sbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5pbXBvcnQge2Nhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzJ1xuXG5leHBvcnQgY29uc3QgeG1sbnMgPSBjcmVhdGUoe1xuICBhdHRyaWJ1dGVzOiB7eG1sbnN4bGluazogJ3htbG5zOnhsaW5rJ30sXG4gIHByb3BlcnRpZXM6IHt4bWxuc1hMaW5rOiBudWxsLCB4bWxuczogbnVsbH0sXG4gIHNwYWNlOiAneG1sbnMnLFxuICB0cmFuc2Zvcm06IGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xmlns.js\n");

/***/ })

};
;