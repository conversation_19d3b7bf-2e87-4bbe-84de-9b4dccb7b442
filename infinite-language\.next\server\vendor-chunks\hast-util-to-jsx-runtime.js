"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-jsx-runtime";
exports.ids = ["vendor-chunks/hast-util-to-jsx-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-jsx-runtime/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toJsxRuntime: () => (/* binding */ toJsxRuntime)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(ssr)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var style_to_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! style-to-js */ \"(ssr)/./node_modules/style-to-js/cjs/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Options, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map()\n\nconst cap = /[A-Z]/g\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\nconst tableCellElement = new Set(['td', 'th'])\n\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime'\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nfunction toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options')\n  }\n\n  const filePath = options.filePath || undefined\n  /** @type {Create} */\n  let create\n\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError(\n        'Expected `jsxDEV` in options when `development: true`'\n      )\n    }\n\n    create = developmentCreate(filePath, options.jsxDEV)\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options')\n    }\n\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options')\n    }\n\n    create = productionCreate(filePath, options.jsx, options.jsxs)\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  }\n\n  const result = one(state, tree, undefined)\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(\n    tree,\n    state.Fragment,\n    {children: result || undefined},\n    undefined\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key)\n  }\n\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node)\n  }\n\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key)\n  }\n\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node)\n  }\n\n  if (node.type === 'root') {\n    return root(state, node, key)\n  }\n\n  if (node.type === 'text') {\n    return text(state, node)\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type = findComponentFromName(state, node.tagName, false)\n  const props = createElementProps(state, node)\n  let children = createChildren(state, node)\n\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(child) : true\n    })\n  }\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree\n    const expression = program.body[0]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateExpression(expression.expression)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateProgram(node.data.estree)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type =\n    node.name === null\n      ? state.Fragment\n      : findComponentFromName(state, node.name, true)\n  const props = createJsxElementProps(state, node)\n  const children = createChildren(state, node)\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {}\n\n  addChildren(props, createChildren(state, node))\n\n  return state.create(node, state.Fragment, props, key)\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0]\n\n    if (value) {\n      props.children = value\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const fn = isStaticChildren ? jsxs : jsx\n    return key ? fn(type, props, key) : fn(type, props)\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const point = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_4__.pointStart)(node)\n    return jsxDEV(\n      type,\n      props,\n      key,\n      isStaticChildren,\n      {\n        columnNumber: point ? point.column - 1 : undefined,\n        fileName: filePath,\n        lineNumber: point ? point.line : undefined\n      },\n      undefined\n    )\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {string} */\n  let prop\n\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop])\n\n      if (result) {\n        const [key, value] = result\n\n        if (\n          state.tableCellAlignToStyle &&\n          key === 'align' &&\n          typeof value === 'string' &&\n          tableCellElement.has(node.tagName)\n        ) {\n          alignValue = value\n        } else {\n          props[key] = value\n        }\n      }\n    }\n  }\n\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */ (props.style || (props.style = {}))\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] =\n      alignValue\n  }\n\n  return props\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree\n        const expression = program.body[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n        const objectExpression = expression.expression\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(objectExpression.type === 'ObjectExpression')\n        const property = objectExpression.properties[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(property.type === 'SpreadElement')\n\n        Object.assign(\n          props,\n          state.evaluater.evaluateExpression(property.argument)\n        )\n      } else {\n        crashEstree(state, node.position)\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name\n      /** @type {unknown} */\n      let value\n\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (\n          attribute.value.data &&\n          attribute.value.data.estree &&\n          state.evaluater\n        ) {\n          const program = attribute.value.data.estree\n          const expression = program.body[0]\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n          value = state.evaluater.evaluateExpression(expression.expression)\n        } else {\n          crashEstree(state, node.position)\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */ (value)\n    }\n  }\n\n  return props\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = []\n  let index = -1\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    /** @type {string | undefined} */\n    let key\n\n    if (state.passKeys) {\n      const name =\n        child.type === 'element'\n          ? child.tagName\n          : child.type === 'mdxJsxFlowElement' ||\n              child.type === 'mdxJsxTextElement'\n            ? child.name\n            : undefined\n\n      if (name) {\n        const count = countsByName.get(name) || 0\n        key = name + '-' + count\n        countsByName.set(name, count + 1)\n      }\n    }\n\n    const result = one(state, child, key)\n    if (result !== undefined) children.push(result)\n  }\n\n  return children\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.stringify)(value)\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject =\n      typeof value === 'object' ? value : parseStyle(state, String(value))\n\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject)\n    }\n\n    return ['style', styleObject]\n  }\n\n  return [\n    state.elementAttributeNameCase === 'react' && info.space\n      ? property_information__WEBPACK_IMPORTED_MODULE_8__.hastToReact[info.property] || info.property\n      : info.attribute,\n    value\n  ]\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return style_to_js__WEBPACK_IMPORTED_MODULE_0__(value, {reactCompat: true})\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {}\n    }\n\n    const cause = /** @type {Error} */ (error)\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    })\n    message.file = state.filePath || undefined\n    message.url = docs + '#cannot-parse-style-attribute'\n\n    throw message\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result\n\n  if (!allowExpression) {\n    result = {type: 'Literal', value: name}\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.')\n    let index = -1\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node\n\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(identifiers[index])\n        ? {type: 'Identifier', name: identifiers[index]}\n        : {type: 'Literal', value: identifiers[index]}\n      node = node\n        ? {\n            type: 'MemberExpression',\n            object: node,\n            property: prop,\n            computed: Boolean(index && prop.type === 'Literal'),\n            optional: false\n          }\n        : prop\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(node, 'always a result')\n    result = node\n  } else {\n    result =\n      (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(name) && !/^[a-z]/.test(name)\n        ? {type: 'Identifier', name}\n        : {type: 'Literal', value: name}\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */ (result.value)\n    return own.call(state.components, name) ? state.components[name] : name\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result)\n  }\n\n  crashEstree(state)\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage(\n    'Cannot handle MDX estrees without `createEvaluater`',\n    {\n      ancestors: state.ancestors,\n      place,\n      ruleId: 'mdx-estree',\n      source: 'hast-util-to-jsx-runtime'\n    }\n  )\n  message.file = state.filePath || undefined\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater'\n\n  throw message\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\n");

/***/ })

};
;