"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nclass EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */\n  add(index, remove, add) {\n    addImplementation(this, index, remove, add)\n  }\n\n  // To do: add this when moving to `micromark`.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {undefined}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImplementation(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */\n  consume(events) {\n    this.map.sort(function (a, b) {\n      return a[0] - b[0]\n    })\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(\n        events.slice(this.map[index][0] + this.map[index][1]),\n        this.map[index][2]\n      )\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push(events.slice())\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      for (const element of slice) {\n        events.push(element)\n      }\n\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */\nfunction addImplementation(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used by tables, use when moving to micromark.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: micromark@5: use `infer` here, when all events are exposed.\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */\nfunction gfmTableHtml() {\n  return {\n    enter: {\n      table(token) {\n        const tableAlign = token._align\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `_align`')\n        this.lineEndingIfNeeded()\n        this.tag('<table>')\n        this.setData('tableAlign', tableAlign)\n      },\n      tableBody() {\n        this.tag('<tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n\n        if (align === undefined) {\n          // Capture results to ignore them.\n          this.buffer()\n        } else {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + align + '>')\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('<thead>')\n      },\n      tableHeader() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n        this.lineEndingIfNeeded()\n        this.tag('<th' + align + '>')\n      },\n      tableRow() {\n        this.setData('tableColumn', 0)\n        this.lineEndingIfNeeded()\n        this.tag('<tr>')\n      }\n    },\n    exit: {\n      // Overwrite the default code text data handler to unescape escaped pipes when\n      // they are in tables.\n      codeTextData(token) {\n        let value = this.sliceSerialize(token)\n\n        if (this.getData('tableAlign')) {\n          value = value.replace(/\\\\([\\\\|])/g, replace)\n        }\n\n        this.raw(this.encode(value))\n      },\n      table() {\n        this.setData('tableAlign')\n        // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n        // but we do need to reset it to match a funky newline GH generates for\n        // list items combined with tables.\n        this.setData('slurpAllLineEndings')\n        this.lineEndingIfNeeded()\n        this.tag('</table>')\n      },\n      tableBody() {\n        this.lineEndingIfNeeded()\n        this.tag('</tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        if (tableColumn in tableAlign) {\n          this.tag('</td>')\n          this.setData('tableColumn', tableColumn + 1)\n        } else {\n          // Stop capturing.\n          this.resume()\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('</thead>')\n      },\n      tableHeader() {\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        this.tag('</th>')\n        this.setData('tableColumn', tableColumn + 1)\n      },\n      tableRow() {\n        const tableAlign = this.getData('tableAlign')\n        let tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        while (tableColumn < tableAlign.length) {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n          tableColumn++\n        }\n\n        this.setData('tableColumn', tableColumn)\n        this.lineEndingIfNeeded()\n        this.tag('</tr>')\n      }\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFibGUvZGV2L2xpYi9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLGVBQWU7QUFDM0I7O0FBRW1DOztBQUVuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMkNBQU07QUFDZDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDJDQUFNO0FBQ2QsUUFBUSwyQ0FBTTtBQUNkOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMkNBQU07QUFDZCxRQUFRLDJDQUFNO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsUUFBUSwyQ0FBTTtBQUNkLFFBQVEsMkNBQU07O0FBRWQ7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLFFBQVEsMkNBQU07QUFDZDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMkNBQU07QUFDZCxRQUFRLDJDQUFNOztBQUVkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhYmxlXFxkZXZcXGxpYlxcaHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0h0bWxFeHRlbnNpb259IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5cbmNvbnN0IGFsaWdubWVudCA9IHtcbiAgbm9uZTogJycsXG4gIGxlZnQ6ICcgYWxpZ249XCJsZWZ0XCInLFxuICByaWdodDogJyBhbGlnbj1cInJpZ2h0XCInLFxuICBjZW50ZXI6ICcgYWxpZ249XCJjZW50ZXJcIidcbn1cblxuLy8gVG8gZG86IG1pY3JvbWFya0A1OiB1c2UgYGluZmVyYCBoZXJlLCB3aGVuIGFsbCBldmVudHMgYXJlIGV4cG9zZWQuXG5cbi8qKlxuICogQ3JlYXRlIGFuIEhUTUwgZXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0byBzdXBwb3J0IEdpdEh1YiB0YWJsZXMgd2hlblxuICogc2VyaWFsaXppbmcgdG8gSFRNTC5cbiAqXG4gKiBAcmV0dXJucyB7SHRtbEV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGh0bWxFeHRlbnNpb25zYCB0b1xuICogICBzdXBwb3J0IEdpdEh1YiB0YWJsZXMgd2hlbiBzZXJpYWxpemluZyB0byBIVE1MLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2ZtVGFibGVIdG1sKCkge1xuICByZXR1cm4ge1xuICAgIGVudGVyOiB7XG4gICAgICB0YWJsZSh0b2tlbikge1xuICAgICAgICBjb25zdCB0YWJsZUFsaWduID0gdG9rZW4uX2FsaWduXG4gICAgICAgIGFzc2VydCh0YWJsZUFsaWduLCAnZXhwZWN0ZWQgYF9hbGlnbmAnKVxuICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgIHRoaXMudGFnKCc8dGFibGU+JylcbiAgICAgICAgdGhpcy5zZXREYXRhKCd0YWJsZUFsaWduJywgdGFibGVBbGlnbilcbiAgICAgIH0sXG4gICAgICB0YWJsZUJvZHkoKSB7XG4gICAgICAgIHRoaXMudGFnKCc8dGJvZHk+JylcbiAgICAgIH0sXG4gICAgICB0YWJsZURhdGEoKSB7XG4gICAgICAgIGNvbnN0IHRhYmxlQWxpZ24gPSB0aGlzLmdldERhdGEoJ3RhYmxlQWxpZ24nKVxuICAgICAgICBjb25zdCB0YWJsZUNvbHVtbiA9IHRoaXMuZ2V0RGF0YSgndGFibGVDb2x1bW4nKVxuICAgICAgICBhc3NlcnQodGFibGVBbGlnbiwgJ2V4cGVjdGVkIGB0YWJsZUFsaWduYCcpXG4gICAgICAgIGFzc2VydCh0eXBlb2YgdGFibGVDb2x1bW4gPT09ICdudW1iZXInLCAnZXhwZWN0ZWQgYHRhYmxlQ29sdW1uYCcpXG4gICAgICAgIGNvbnN0IGFsaWduID0gYWxpZ25tZW50W3RhYmxlQWxpZ25bdGFibGVDb2x1bW5dXVxuXG4gICAgICAgIGlmIChhbGlnbiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgLy8gQ2FwdHVyZSByZXN1bHRzIHRvIGlnbm9yZSB0aGVtLlxuICAgICAgICAgIHRoaXMuYnVmZmVyKClcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgICAgdGhpcy50YWcoJzx0ZCcgKyBhbGlnbiArICc+JylcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHRhYmxlSGVhZCgpIHtcbiAgICAgICAgdGhpcy5saW5lRW5kaW5nSWZOZWVkZWQoKVxuICAgICAgICB0aGlzLnRhZygnPHRoZWFkPicpXG4gICAgICB9LFxuICAgICAgdGFibGVIZWFkZXIoKSB7XG4gICAgICAgIGNvbnN0IHRhYmxlQWxpZ24gPSB0aGlzLmdldERhdGEoJ3RhYmxlQWxpZ24nKVxuICAgICAgICBjb25zdCB0YWJsZUNvbHVtbiA9IHRoaXMuZ2V0RGF0YSgndGFibGVDb2x1bW4nKVxuICAgICAgICBhc3NlcnQodGFibGVBbGlnbiwgJ2V4cGVjdGVkIGB0YWJsZUFsaWduYCcpXG4gICAgICAgIGFzc2VydCh0eXBlb2YgdGFibGVDb2x1bW4gPT09ICdudW1iZXInLCAnZXhwZWN0ZWQgYHRhYmxlQ29sdW1uYCcpXG4gICAgICAgIGNvbnN0IGFsaWduID0gYWxpZ25tZW50W3RhYmxlQWxpZ25bdGFibGVDb2x1bW5dXVxuICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgIHRoaXMudGFnKCc8dGgnICsgYWxpZ24gKyAnPicpXG4gICAgICB9LFxuICAgICAgdGFibGVSb3coKSB7XG4gICAgICAgIHRoaXMuc2V0RGF0YSgndGFibGVDb2x1bW4nLCAwKVxuICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgIHRoaXMudGFnKCc8dHI+JylcbiAgICAgIH1cbiAgICB9LFxuICAgIGV4aXQ6IHtcbiAgICAgIC8vIE92ZXJ3cml0ZSB0aGUgZGVmYXVsdCBjb2RlIHRleHQgZGF0YSBoYW5kbGVyIHRvIHVuZXNjYXBlIGVzY2FwZWQgcGlwZXMgd2hlblxuICAgICAgLy8gdGhleSBhcmUgaW4gdGFibGVzLlxuICAgICAgY29kZVRleHREYXRhKHRva2VuKSB7XG4gICAgICAgIGxldCB2YWx1ZSA9IHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG5cbiAgICAgICAgaWYgKHRoaXMuZ2V0RGF0YSgndGFibGVBbGlnbicpKSB7XG4gICAgICAgICAgdmFsdWUgPSB2YWx1ZS5yZXBsYWNlKC9cXFxcKFtcXFxcfF0pL2csIHJlcGxhY2UpXG4gICAgICAgIH1cblxuICAgICAgICB0aGlzLnJhdyh0aGlzLmVuY29kZSh2YWx1ZSkpXG4gICAgICB9LFxuICAgICAgdGFibGUoKSB7XG4gICAgICAgIHRoaXMuc2V0RGF0YSgndGFibGVBbGlnbicpXG4gICAgICAgIC8vIE5vdGU6IHdlIGRvbuKAmXQgc2V0IGBzbHVycEFsbExpbmVFbmRpbmdzYCBhbnltb3JlLCBpbiBkZWxpbWl0ZXIgcm93cyxcbiAgICAgICAgLy8gYnV0IHdlIGRvIG5lZWQgdG8gcmVzZXQgaXQgdG8gbWF0Y2ggYSBmdW5reSBuZXdsaW5lIEdIIGdlbmVyYXRlcyBmb3JcbiAgICAgICAgLy8gbGlzdCBpdGVtcyBjb21iaW5lZCB3aXRoIHRhYmxlcy5cbiAgICAgICAgdGhpcy5zZXREYXRhKCdzbHVycEFsbExpbmVFbmRpbmdzJylcbiAgICAgICAgdGhpcy5saW5lRW5kaW5nSWZOZWVkZWQoKVxuICAgICAgICB0aGlzLnRhZygnPC90YWJsZT4nKVxuICAgICAgfSxcbiAgICAgIHRhYmxlQm9keSgpIHtcbiAgICAgICAgdGhpcy5saW5lRW5kaW5nSWZOZWVkZWQoKVxuICAgICAgICB0aGlzLnRhZygnPC90Ym9keT4nKVxuICAgICAgfSxcbiAgICAgIHRhYmxlRGF0YSgpIHtcbiAgICAgICAgY29uc3QgdGFibGVBbGlnbiA9IHRoaXMuZ2V0RGF0YSgndGFibGVBbGlnbicpXG4gICAgICAgIGNvbnN0IHRhYmxlQ29sdW1uID0gdGhpcy5nZXREYXRhKCd0YWJsZUNvbHVtbicpXG4gICAgICAgIGFzc2VydCh0YWJsZUFsaWduLCAnZXhwZWN0ZWQgYHRhYmxlQWxpZ25gJylcbiAgICAgICAgYXNzZXJ0KHR5cGVvZiB0YWJsZUNvbHVtbiA9PT0gJ251bWJlcicsICdleHBlY3RlZCBgdGFibGVDb2x1bW5gJylcblxuICAgICAgICBpZiAodGFibGVDb2x1bW4gaW4gdGFibGVBbGlnbikge1xuICAgICAgICAgIHRoaXMudGFnKCc8L3RkPicpXG4gICAgICAgICAgdGhpcy5zZXREYXRhKCd0YWJsZUNvbHVtbicsIHRhYmxlQ29sdW1uICsgMSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBTdG9wIGNhcHR1cmluZy5cbiAgICAgICAgICB0aGlzLnJlc3VtZSgpXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICB0YWJsZUhlYWQoKSB7XG4gICAgICAgIHRoaXMubGluZUVuZGluZ0lmTmVlZGVkKClcbiAgICAgICAgdGhpcy50YWcoJzwvdGhlYWQ+JylcbiAgICAgIH0sXG4gICAgICB0YWJsZUhlYWRlcigpIHtcbiAgICAgICAgY29uc3QgdGFibGVDb2x1bW4gPSB0aGlzLmdldERhdGEoJ3RhYmxlQ29sdW1uJylcbiAgICAgICAgYXNzZXJ0KHR5cGVvZiB0YWJsZUNvbHVtbiA9PT0gJ251bWJlcicsICdleHBlY3RlZCBgdGFibGVDb2x1bW5gJylcbiAgICAgICAgdGhpcy50YWcoJzwvdGg+JylcbiAgICAgICAgdGhpcy5zZXREYXRhKCd0YWJsZUNvbHVtbicsIHRhYmxlQ29sdW1uICsgMSlcbiAgICAgIH0sXG4gICAgICB0YWJsZVJvdygpIHtcbiAgICAgICAgY29uc3QgdGFibGVBbGlnbiA9IHRoaXMuZ2V0RGF0YSgndGFibGVBbGlnbicpXG4gICAgICAgIGxldCB0YWJsZUNvbHVtbiA9IHRoaXMuZ2V0RGF0YSgndGFibGVDb2x1bW4nKVxuICAgICAgICBhc3NlcnQodGFibGVBbGlnbiwgJ2V4cGVjdGVkIGB0YWJsZUFsaWduYCcpXG4gICAgICAgIGFzc2VydCh0eXBlb2YgdGFibGVDb2x1bW4gPT09ICdudW1iZXInLCAnZXhwZWN0ZWQgYHRhYmxlQ29sdW1uYCcpXG5cbiAgICAgICAgd2hpbGUgKHRhYmxlQ29sdW1uIDwgdGFibGVBbGlnbi5sZW5ndGgpIHtcbiAgICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgICAgdGhpcy50YWcoJzx0ZCcgKyBhbGlnbm1lbnRbdGFibGVBbGlnblt0YWJsZUNvbHVtbl1dICsgJz48L3RkPicpXG4gICAgICAgICAgdGFibGVDb2x1bW4rK1xuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy5zZXREYXRhKCd0YWJsZUNvbHVtbicsIHRhYmxlQ29sdW1uKVxuICAgICAgICB0aGlzLmxpbmVFbmRpbmdJZk5lZWRlZCgpXG4gICAgICAgIHRoaXMudGFnKCc8L3RyPicpXG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9ICQwXG4gKiBAcGFyYW0ge3N0cmluZ30gJDFcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIHJlcGxhY2UoJDAsICQxKSB7XG4gIC8vIFBpcGVzIHdvcmssIGJhY2tzbGFzaGVzIGRvbuKAmXQgKGJ1dCBjYW7igJl0IGVzY2FwZSBwaXBlcykuXG4gIHJldHVybiAkMSA9PT0gJ3wnID8gJDEgOiAkMFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n/**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */\n\n\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */\nfunction gfmTableAlign(events, index) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\n\n\n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */\nfunction gfmTable() {\n  return {\n    flow: {\n      null: {name: 'table', tokenize: tokenizeTable, resolveAll: resolveTable}\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterValueBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterCellAfter,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n        ? 'tableDelimiter'\n        : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText\n      start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;