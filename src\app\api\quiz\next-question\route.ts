import { NextRequest, NextResponse } from 'next/server';
import { Level, Language } from '@/types';
import { generateQuestionWithGemini } from '@/lib/gemini';

export async function POST(request: NextRequest) {
  try {
    const { level, language = 'vi' } = await request.json();

    // Validate level
    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    // Validate language
    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    // Generate question using Gemini AI
    const question = await generateQuestionWithGemini(level as Level, language as Language);

    return NextResponse.json({
      success: true,
      question
    });

  } catch (error) {
    console.error('Error generating next question:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate question' },
      { status: 500 }
    );
  }
}
