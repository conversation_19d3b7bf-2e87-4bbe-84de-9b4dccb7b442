# Redis Setup for Question Storage

## Overview

The application now uses Redis to store temporary question data instead of in-memory storage. This provides better scalability, persistence, and automatic cleanup capabilities.

## Features

- **Automatic TTL**: Questions expire after 1 hour automatically
- **One-time use**: Questions are automatically deleted after being answered
- **Fallback mechanism**: Falls back to memory storage if <PERSON><PERSON> is unavailable
- **Periodic cleanup**: Runs cleanup every 30 minutes to remove expired questions
- **Admin endpoints**: Monitor and manage Redis data

## Setup

### 1. Install Redis

#### Windows (using WSL or Docker)
```bash
# Using Docker
docker run -d --name redis -p 6379:6379 redis:latest

# Or using WSL
sudo apt update
sudo apt install redis-server
sudo service redis-server start
```

#### macOS
```bash
brew install redis
brew services start redis
```

#### Linux
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2. Environment Configuration

Add to your `.env.local` file:
```env
REDIS_URL=redis://localhost:6379
```

For production, use your Redis provider's URL:
```env
REDIS_URL=redis://username:password@hostname:port
```

### 3. Verify Installation

Test Redis connection:
```bash
redis-cli ping
# Should return: PONG
```

## Usage

### Question Storage

Questions are automatically stored with:
- **Key format**: `question:{questionId}`
- **TTL**: 1 hour (3600 seconds)
- **Data**: JSON containing correctAnswer, explanation, level, language, createdAt
- **One-time use**: Questions are automatically deleted when answered via `getAndDelete()` method

### Question Lifecycle

1. **Creation**: Question generated and stored in Redis with 1-hour TTL
2. **Answer submission**: Question retrieved and immediately deleted from Redis
3. **Cleanup**: Any remaining questions cleaned up automatically after TTL expires

### Admin Endpoints

#### Get Statistics
```bash
GET /api/admin/redis?action=stats
```

Response:
```json
{
  "success": true,
  "stats": {
    "totalQuestions": 5,
    "oldestQuestion": 1703123456789,
    "oldestQuestionAge": 15
  }
}
```

#### Manual Cleanup
```bash
GET /api/admin/redis?action=cleanup
```

Response:
```json
{
  "success": true,
  "message": "Cleaned up 3 expired questions"
}
```

#### Delete Specific Question
```bash
DELETE /api/admin/redis
Content-Type: application/json

{
  "questionId": "question-uuid-here"
}
```

## Monitoring

### Redis CLI Commands

```bash
# Connect to Redis
redis-cli

# List all question keys
KEYS question:*

# Get question data
GET question:your-question-id

# Check TTL (time to live)
TTL question:your-question-id

# Get Redis info
INFO memory
```

### Application Logs

The application logs Redis operations:
- Connection status
- Storage operations
- Cleanup activities
- Fallback usage

## Troubleshooting

### Redis Connection Issues

1. **Check Redis is running**:
   ```bash
   redis-cli ping
   ```

2. **Check port availability**:
   ```bash
   netstat -an | grep 6379
   ```

3. **Check environment variables**:
   ```bash
   echo $REDIS_URL
   ```

### Fallback Behavior

If Redis is unavailable, the application automatically falls back to in-memory storage:
- Questions stored in Map object
- No persistence across server restarts
- No automatic cleanup
- Logs indicate fallback usage

### Performance Considerations

- **Memory usage**: Monitor Redis memory with `INFO memory`
- **Connection pooling**: Redis client handles connection pooling automatically
- **TTL efficiency**: Redis automatically removes expired keys
- **Cleanup frequency**: Adjust cleanup interval based on usage patterns

## Production Recommendations

1. **Use Redis Cloud or managed service**
2. **Enable Redis persistence** (RDB or AOF)
3. **Set up monitoring** (Redis Insight, CloudWatch, etc.)
4. **Configure backup strategy**
5. **Use Redis Cluster** for high availability
6. **Monitor memory usage** and set maxmemory policies

## Migration from Memory Storage

The migration is automatic:
- Old in-memory storage is replaced
- No data migration needed (questions are temporary)
- Fallback ensures compatibility
- No breaking changes to API endpoints
