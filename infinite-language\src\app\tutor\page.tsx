import { Metadata } from 'next';
import TutorClient from '@/components/TutorClient';

export const metadata: Metadata = {
  title: 'AI English Tutor - Infinite English',
  description: 'Get personalized help from our AI English tutor. Ask questions about grammar, vocabulary, pronunciation, and get instant explanations and study recommendations.',
  keywords: 'AI tutor, English tutor, grammar help, vocabulary help, pronunciation guide, personalized learning, English questions',
  openGraph: {
    title: 'AI English Tutor - Infinite English',
    description: 'Get personalized help from our AI English tutor with instant explanations and study recommendations.',
    type: 'website',
  },
};

export default function TutorPage() {
  return <TutorClient />;
}
