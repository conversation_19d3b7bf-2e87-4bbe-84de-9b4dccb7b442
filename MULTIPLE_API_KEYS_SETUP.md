# 🔑 Multiple Gemini API Keys Setup

## 📋 Overview
Hệ thống đã được cập nhật để hỗ trợ nhiều Gemini API key nhằm tránh bị giới hạn RPM (Requests Per Minute). Hệ thống sẽ tự động:

- Chọn ngẫu nhiên API key cho mỗi request
- Retry với key khác khi gặp rate limit (429 error)
- Log thông tin key đang sử dụng để debug

## ⚙️ Cấu hình

### 1. Environment Variable

Trong file `.env.local`, thêm nhiều API key cách nhau bằng dấu phẩy:

```env
# Single API key (cách cũ - vẫn hoạt động)
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM

# Multiple API keys (cách mới - khuyến nghị)
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM,AIzaSyAnotherKey123456789,AIzaSyThirdKey987654321
```

### 2. L<PERSON>y thêm API Keys

1. Truy cập [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Tạo thêm API key mới
3. Copy và thêm vào environment variable

## 🚀 Cách hoạt động

### Random Key Selection
```javascript
// Hệ thống sẽ chọn ngẫu nhiên 1 trong các key có sẵn
🎲 Using API key 2/3 (AIzaSyCWnY...)
```

### Rate Limit Handling
```javascript
// Khi gặp rate limit, tự động thử key khác
❌ Error with API key AIzaSyCWnY...: 429 rate limit
⏳ Rate limit detected, trying with different API key...
🚀 Attempt 2/3 with key AIzaSyAnot...
✅ Successfully generated content with key AIzaSyAnot...
```

### Logging
Hệ thống sẽ log:
- Số lượng key được load: `🔑 Loaded 3 Gemini API key(s)`
- Key đang sử dụng: `🎲 Using API key 1/3 (AIzaSyCWnY...)`
- Kết quả thành công: `✅ Successfully generated content`
- Lỗi và retry: `❌ Error... ⏳ Rate limit detected...`

## 🔧 Tính năng

### 1. Load Balancing
- Phân tán request đều trên các API key
- Giảm tải cho từng key riêng lẻ

### 2. Fault Tolerance
- Tự động retry với key khác khi gặp lỗi
- Không bị gián đoạn service khi 1 key bị limit

### 3. Monitoring
- Log chi tiết để theo dõi usage
- Dễ dàng debug khi có vấn đề

## 📊 Lợi ích

### Tăng Throughput
- **Trước**: 1 key = ~60 requests/minute
- **Sau**: 3 keys = ~180 requests/minute

### Reliability
- Backup khi key chính bị limit
- Continuous service availability

### Cost Optimization
- Sử dụng hiệu quả quota của từng key
- Tránh waste khi 1 key bị limit

## 🛠️ Troubleshooting

### Kiểm tra Keys
```bash
# Xem log khi start server
🔑 Loaded 3 Gemini API key(s)
```

### Debug Rate Limits
```bash
# Xem log khi có rate limit
❌ Error with API key AIzaSyCWnY...: 429 rate limit
⏳ Rate limit detected, trying with different API key...
```

### Validate Keys
Đảm bảo tất cả keys đều valid:
- Không có space thừa
- Đúng format AIzaSy...
- Đã enable Gemini API

## 📝 Best Practices

### 1. Key Management
- Sử dụng ít nhất 3-5 keys cho production
- Rotate keys định kỳ
- Monitor usage của từng key

### 2. Environment Setup
```env
# Development
GEMINI_API_KEY=key1,key2

# Production  
GEMINI_API_KEY=key1,key2,key3,key4,key5
```

### 3. Monitoring
- Theo dõi log để biết key nào bị limit
- Adjust số lượng key dựa trên traffic

## 🔄 Migration

### Từ Single Key
```env
# Cũ
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM

# Mới (backward compatible)
GEMINI_API_KEY=AIzaSyCWnYPSaCqM-NiK8qKjrYd_ciCRWpWQfZM,AIzaSyNewKey123
```

### Testing
```bash
# Test với multiple keys
npm run dev

# Xem log để confirm
🔑 Loaded 2 Gemini API key(s)
🎲 Using API key 1/2 (AIzaSyCWnY...)
```

## ⚠️ Lưu ý

1. **Security**: Không commit API keys vào git
2. **Quota**: Mỗi key có limit riêng, không share quota
3. **Billing**: Mỗi key tính phí riêng
4. **Rate Limit**: Vẫn có thể bị limit nếu tất cả keys đều hết quota

## 🎯 Kết quả

Với setup này, bạn sẽ có:
- ✅ Tăng throughput đáng kể
- ✅ Giảm downtime do rate limit  
- ✅ Better user experience
- ✅ Scalable architecture
