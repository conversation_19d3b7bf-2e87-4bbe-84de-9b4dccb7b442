// Test script to verify multiple API keys functionality
// Run with: node test-multiple-keys.js

require('dotenv').config({ path: '.env.local' });

async function testMultipleKeys() {
  console.log('🧪 Testing Multiple Gemini API Keys System...\n');

  // Check environment variable
  const apiKeysString = process.env.GEMINI_API_KEY;
  if (!apiKeysString) {
    console.error('❌ GEMINI_API_KEY not found in environment variables');
    return;
  }

  // Parse keys
  const apiKeys = apiKeysString.split(',').map(key => key.trim()).filter(key => key.length > 0);
  
  console.log(`🔑 Found ${apiKeys.length} API key(s):`);
  apiKeys.forEach((key, index) => {
    console.log(`   ${index + 1}. ${key.substring(0, 10)}...${key.substring(key.length - 4)}`);
  });

  if (apiKeys.length === 1) {
    console.log('\n⚠️  Only 1 API key found. Consider adding more keys for better rate limit handling.');
    console.log('   Example: GEMINI_API_KEY=key1,key2,key3');
  } else {
    console.log(`\n✅ Multiple keys configured! This will help avoid rate limits.`);
  }

  // Test key selection randomness
  console.log('\n🎲 Testing random key selection (10 attempts):');
  const keyUsageCount = {};
  
  for (let i = 0; i < 10; i++) {
    const randomIndex = Math.floor(Math.random() * apiKeys.length);
    const selectedKey = apiKeys[randomIndex];
    const keyId = `${selectedKey.substring(0, 10)}...`;
    
    keyUsageCount[keyId] = (keyUsageCount[keyId] || 0) + 1;
    console.log(`   Attempt ${i + 1}: Key ${randomIndex + 1}/${apiKeys.length} (${keyId})`);
  }

  console.log('\n📊 Key usage distribution:');
  Object.entries(keyUsageCount).forEach(([keyId, count]) => {
    const percentage = ((count / 10) * 100).toFixed(1);
    console.log(`   ${keyId}: ${count}/10 times (${percentage}%)`);
  });

  // Test actual API call if possible
  console.log('\n🚀 Testing actual API call...');
  try {
    // Import the function (this might not work in Node.js without proper setup)
    // const { generateQuestionWithGemini } = require('./src/lib/gemini.ts');
    // const question = await generateQuestionWithGemini('beginner', 'en');
    // console.log('✅ API call successful!');
    // console.log(`   Question: ${question.question.substring(0, 50)}...`);
    
    console.log('ℹ️  Skipping actual API test (requires TypeScript compilation)');
    console.log('   To test API calls, run: npm run dev and check the logs');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }

  console.log('\n🎯 Summary:');
  console.log(`   ✅ ${apiKeys.length} API key(s) loaded successfully`);
  console.log(`   ✅ Random selection working`);
  console.log(`   ✅ Environment setup correct`);
  
  if (apiKeys.length >= 3) {
    console.log(`   ✅ Good key count for production (${apiKeys.length} keys)`);
  } else if (apiKeys.length === 2) {
    console.log(`   ⚠️  Consider adding more keys for better reliability`);
  } else {
    console.log(`   ⚠️  Single key setup - add more keys to avoid rate limits`);
  }

  console.log('\n📝 Next steps:');
  console.log('   1. Start the development server: npm run dev');
  console.log('   2. Generate some questions to see key rotation in action');
  console.log('   3. Check console logs for key usage patterns');
  console.log('   4. Monitor for rate limit handling');
}

// Utility function to validate API key format
function validateApiKey(key) {
  // Basic validation for Gemini API key format
  const isValid = key.startsWith('AIzaSy') && key.length >= 35;
  return isValid;
}

// Extended validation
function validateAllKeys() {
  console.log('\n🔍 Validating API key formats...');
  
  const apiKeysString = process.env.GEMINI_API_KEY;
  if (!apiKeysString) return;

  const apiKeys = apiKeysString.split(',').map(key => key.trim()).filter(key => key.length > 0);
  
  apiKeys.forEach((key, index) => {
    const isValid = validateApiKey(key);
    const status = isValid ? '✅' : '❌';
    const keyPreview = `${key.substring(0, 10)}...${key.substring(key.length - 4)}`;
    console.log(`   Key ${index + 1}: ${status} ${keyPreview} ${isValid ? '(Valid format)' : '(Invalid format)'}`);
  });
}

// Run tests
async function runAllTests() {
  try {
    await testMultipleKeys();
    validateAllKeys();
    
    console.log('\n🎉 All tests completed!');
    console.log('\nFor more information, see: MULTIPLE_API_KEYS_SETUP.md');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testMultipleKeys,
  validateApiKey,
  validateAllKeys
};
